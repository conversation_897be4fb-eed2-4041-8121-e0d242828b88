{"name": "nearest-dearest", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"prepare": "husky", "start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix"}, "lint-staged": {"*.{js,jsx,ts,tsx,json,css,md}": ["prettier --write", "eslint --fix"]}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/core": "^6.4.17", "@react-navigation/native": "^7.0.14", "@react-navigation/stack": "^6.4.1", "expo": "^53.0.0", "expo-constants": "~17.1.6", "expo-font": "~13.3.1", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-location": "~18.1.5", "expo-notifications": "~0.31.2", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.7", "expo-web-browser": "~14.1.6", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.2", "react-native-dotenv": "^3.4.11", "react-native-drawer-layout": "^3.3.2", "react-native-gesture-handler": "~2.24.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-maps": "1.20.1", "react-native-permissions": "^4.1.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-web": "^0.20.0", "zustand": "^5.0.0-rc.2"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/jest": "^29.5.12", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.0.7", "babel-preset-expo": "~13.0.0", "eslint": "^8.57.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jsx-a11y": "^6.10.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.6", "jest": "^29.2.1", "jest-expo": "~53.0.5", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "react-test-renderer": "18.2.0", "typescript": "~5.8.3"}, "overrides": {"react": "19.0.0", "react-dom": "19.0.0", "@types/react": "~19.0.10", "@react-navigation/native": "^7.0.14", "jest-expo": "~53.0.5", "react-test-renderer": {"react": "19.0.0"}}, "private": true}