{"expo": {"newArchEnabled": true, "notification": {"icon": "./assets/images/react-logo.png", "color": "#ffffff"}, "name": "nearest-dearest", "slug": "nearest-dearest", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.omnissiah.nearest-dearest"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-location", {"locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location."}], ["expo-secure-store", {"faceIDPermission": "Allow $(PRODUCT_NAME) to access your Face ID biometric data."}], "expo-font"], "experiments": {"typedRoutes": true}}}