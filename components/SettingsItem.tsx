// components/SettingsItem.tsx

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';

interface SettingsItemProps {
  icon: string;
  title: string;
  description?: string;
  onPress?: () => void;
  rightComponent?: React.ReactNode;
  style?: ViewStyle;
  textColor?: string;
  iconColor?: string;
}

const SettingsItem: React.FC<SettingsItemProps> = ({
  icon,
  title,
  description,
  onPress,
  rightComponent,
  style,
  textColor = '#333',
  iconColor = '#007AFF',
}) => {
  return (
    <TouchableOpacity
      style={[styles.container, style]}
      onPress={onPress}
      disabled={!onPress}
      accessibilityLabel={title}
      accessibilityRole={onPress ? 'button' : 'none'}
    >
      {/* Icon */}
      <Ionicons name={icon} size={24} color={iconColor} style={styles.icon} />

      {/* Title and Description */}
      <View style={styles.textContainer}>
        <Text style={[styles.title, { color: textColor }]}>{title}</Text>
        {description ? (
          <Text style={[styles.description, { color: textColor + '99' }]}>{description}</Text>
        ) : null}
      </View>

      {/* Right Component */}
      {rightComponent ? (
        rightComponent
      ) : onPress ? (
        <Ionicons name="chevron-forward" size={20} color={textColor + '4D'} />
      ) : null}
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 15,
    backgroundColor: '#fff',
    borderRadius: 8,
    marginVertical: 5,
    marginHorizontal: 16,
    elevation: 1, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 2,
  },
  icon: {
    marginRight: 15,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    color: '#333',
  },
  description: {
    fontSize: 14,
    color: '#666',
    marginTop: 4,
  },
});

export default SettingsItem;
