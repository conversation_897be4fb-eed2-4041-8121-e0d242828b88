import React from 'react';
import {
  Modal,
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  Image,
} from 'react-native';
import { USERS } from '@/constants/Users';

interface AddTaskModalProps {
  visible: boolean;
  onClose: () => void;
  title: string;
  setTitle: (title: string) => void;
  description: string;
  setDescription: (description: string) => void;
  assignee: 'user_1' | 'user_2';
  setAssignee: (assignee: 'user_1' | 'user_2') => void;
  onSave: () => void;
}

const AddTaskModal: React.FC<AddTaskModalProps> = ({
  visible,
  onClose,
  title,
  setTitle,
  description,
  setDescription,
  assignee,
  setAssignee,
  onSave,
}) => {
  return (
    <Modal
      visible={visible}
      animationType="slide"
      transparent={true}
      onRequestClose={onClose}
    >
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}
        style={styles.overlay}
      >
        <View style={styles.modal}>
          <Text style={styles.title}>Add New Task</Text>
          <TextInput
            style={styles.input}
            placeholder="Task Title"
            placeholderTextColor={'#A4A4A4'}
            value={title}
            onChangeText={setTitle}
            returnKeyType="done"
          />
          <TextInput
            style={[styles.input, styles.description]}
            placeholder="Task Description (Optional)"
            placeholderTextColor={'#A4A4A4'}
            value={description}
            onChangeText={setDescription}
            multiline
          />
          {/* Assignee Selection */}
          <View style={styles.assigneeRow}>
            <Text style={styles.assigneeLabel}>Assign To:</Text>
            <TouchableOpacity
              style={[
                styles.assigneeButton,
                assignee === USERS.self.id && styles.selectedAssigneeButton,
              ]}
              onPress={() => setAssignee(USERS.self.id)}
            >
              <Image
                source={{ uri: USERS.self.avatar }}
                style={styles.avatar}
              />
              <Text
                style={[
                  styles.assigneeText,
                  assignee === USERS.self.id && styles.selectedAssigneeText,
                ]}
              >
                {USERS.self.name}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.assigneeButton,
                assignee === USERS.spouse.id && styles.selectedAssigneeButton,
              ]}
              onPress={() => setAssignee(USERS.spouse.id)}
            >
              <Image
                source={{ uri: USERS.spouse.avatar }}
                style={styles.avatar}
              />
              <Text
                style={[
                  styles.assigneeText,
                  assignee === USERS.spouse.id && styles.selectedAssigneeText,
                ]}
              >
                {USERS.spouse.name}
              </Text>
            </TouchableOpacity>
          </View>
          {/* Buttons */}
          <View style={styles.buttonRow}>
            <TouchableOpacity
              style={[styles.button, styles.cancelButton]}
              onPress={onClose}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.button,
                styles.saveButton,
                { backgroundColor: title.trim() ? '#007AFF' : '#a0c4ff' },
              ]}
              onPress={onSave}
              disabled={!title.trim()}
            >
              <Text style={styles.saveButtonText}>Save</Text>
            </TouchableOpacity>
          </View>
        </View>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default AddTaskModal;

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    padding: 20,
  },
  modal: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    elevation: 5,
    shadowColor: '#000',
    shadowOpacity: 0.3,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
  },
  title: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    color: '#333',
  },
  input: {
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    marginBottom: 15,
    height: 40,
  },
  description: {
    height: 80,
    textAlignVertical: 'top',
  },
  assigneeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  assigneeLabel: {
    fontSize: 16,
    marginRight: 10,
    color: '#333',
  },
  assigneeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderColor: '#007AFF',
    borderWidth: 1,
    borderRadius: 8,
    marginRight: 10,
  },
  selectedAssigneeButton: {
    backgroundColor: '#007AFF',
  },
  avatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 5,
  },
  assigneeText: {
    fontSize: 14,
    color: '#007AFF',
  },
  selectedAssigneeText: {
    color: '#fff',
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: '#FFFFFF',
    borderColor: '#4A4A4A',
    borderWidth: 1,
  },
  cancelButtonText: {
    color: '#4A4A4A',
    fontSize: 16,
  },
  saveButton: {
    backgroundColor: '#007AFF',
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
  },
});
