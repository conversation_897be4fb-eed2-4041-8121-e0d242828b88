// components/TodoItem.tsx

import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { USERS } from '@/constants/Users';
import { useTheme } from '../../app/context/ThemeContext';

interface Todo {
  id: string;
  title: string;
  description?: string;
  assigneeId: string;
  completed: boolean;
}

interface TodoItemProps {
  todo: Todo;
  onToggle: () => void;
  onDelete: () => void;
}

const TodoItem: React.FC<TodoItemProps> = ({ todo, onToggle, onDelete }) => {
  const { theme } = useTheme();
  const assignee = todo.assigneeId === USERS.self.id ? USERS.self : USERS.others[0];

  return (
    <View style={[styles.container, { backgroundColor: theme.card }]}>
      {/* Checkbox */}
      <TouchableOpacity
        onPress={onToggle}
        style={[
          styles.checkbox,
          {
            borderColor: assignee.color,
            backgroundColor: todo.completed ? assignee.color : 'transparent',
          },
        ]}
      >
        {todo.completed && <Ionicons name="checkmark" size={20} color={theme.card} />}
      </TouchableOpacity>

      {/* Task Details */}
      <View style={styles.textContainer}>
        <Text
          style={[styles.title, { color: theme.text }, todo.completed && styles.completedTitle]}
        >
          {todo.title}
        </Text>
        {todo.description ? (
          <Text style={[styles.description, { color: theme.tabBar.inactive }]}>
            {todo.description}
          </Text>
        ) : null}
      </View>

      {/* Assignee Information */}
      <View style={styles.assigneeContainer}>
        <Image source={{ uri: assignee.avatar }} style={styles.avatar} />
        <Text style={[styles.assigneeName, { color: theme.text }]}>{assignee.name}</Text>
      </View>

      {/* Delete Button */}
      <TouchableOpacity onPress={onDelete} style={styles.deleteButton}>
        <Ionicons name="trash" size={20} color={theme.error} />
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    marginVertical: 4,
    marginHorizontal: 16,
    elevation: 1,
    shadowColor: '#000',
    shadowOpacity: 0.05,
    shadowOffset: { width: 0, height: 1 },
    shadowRadius: 3,
  },
  checkbox: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  textContainer: {
    flex: 1,
  },
  title: {
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 20,
  },
  completedTitle: {
    textDecorationLine: 'line-through',
    opacity: 0.6,
  },
  description: {
    fontSize: 14,
    marginTop: 2,
    lineHeight: 18,
  },
  assigneeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginLeft: 12,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  avatar: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginRight: 4,
  },
  assigneeName: {
    fontSize: 12,
    fontWeight: '500',
  },
  deleteButton: {
    marginLeft: 12,
    padding: 4,
  },
});

export default TodoItem;
