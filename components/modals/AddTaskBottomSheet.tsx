import React, { useEffect, useRef } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Modal,
  Animated,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  Image,
  PanResponder,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
// // import { BlurView } from 'expo-blur'; // Temporarily disabled due to Node.js environment issue // Will add this later
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@/app/context/ThemeContext';
import { USERS } from '@/constants/Users';

interface AddTaskBottomSheetProps {
  visible: boolean;
  onClose: () => void;
  onSave: (title: string, description: string, assigneeId: string) => void;
  title: string;
  setTitle: (title: string) => void;
  description: string;
  setDescription: (description: string) => void;
  selectedAssignee: string;
  setSelectedAssignee: (assigneeId: string) => void;
}

const { height: SCREEN_HEIGHT } = Dimensions.get('window');
const MODAL_HEIGHT = SCREEN_HEIGHT * 0.7;

export default function AddTaskBottomSheet({
  visible,
  onClose,
  onSave,
  title,
  setTitle,
  description,
  setDescription,
  selectedAssignee,
  setSelectedAssignee,
}: AddTaskBottomSheetProps) {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const translateY = useRef(new Animated.Value(MODAL_HEIGHT)).current;
  const backdropOpacity = useRef(new Animated.Value(0)).current;
  const spouse = USERS.others[0];

  // Pan responder for swipe to dismiss
  const panResponder = useRef(
    PanResponder.create({
      onMoveShouldSetPanResponder: (_, gestureState) => {
        return gestureState.dy > 0 && Math.abs(gestureState.dy) > Math.abs(gestureState.dx);
      },
      onPanResponderMove: (_, gestureState) => {
        if (gestureState.dy > 0) {
          translateY.setValue(gestureState.dy);
        }
      },
      onPanResponderRelease: (_, gestureState) => {
        if (gestureState.dy > 100 || gestureState.vy > 0.5) {
          handleClose();
        } else {
          Animated.spring(translateY, {
            toValue: 0,
            useNativeDriver: true,
            tension: 100,
            friction: 8,
          }).start();
        }
      },
    }),
  ).current;

  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.spring(translateY, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(backdropOpacity, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const handleClose = () => {
    Animated.parallel([
      Animated.timing(translateY, {
        toValue: MODAL_HEIGHT,
        duration: 250,
        useNativeDriver: true,
      }),
      Animated.timing(backdropOpacity, {
        toValue: 0,
        duration: 250,
        useNativeDriver: true,
      }),
    ]).start(() => {
      onClose();
    });
  };

  const handleSave = () => {
    if (title.trim()) {
      onSave(title, description, selectedAssignee);
      handleClose();
    }
  };

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      transparent
      animationType="none"
      statusBarTranslucent
      onRequestClose={handleClose}
    >
      <View style={{ flex: 1 }}>
        {/* Backdrop */}
        <Animated.View
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
            opacity: backdropOpacity,
          }}
        >
          <TouchableOpacity style={{ flex: 1 }} activeOpacity={1} onPress={handleClose} />
        </Animated.View>

        {/* Bottom Sheet */}
        <Animated.View
          style={{
            position: 'absolute',
            bottom: 0,
            left: 0,
            right: 0,
            height: MODAL_HEIGHT,
            backgroundColor: theme.background,
            borderTopLeftRadius: 20,
            borderTopRightRadius: 20,
            transform: [{ translateY }],
            shadowColor: '#000',
            shadowOffset: { width: 0, height: -4 },
            shadowOpacity: 0.1,
            shadowRadius: 12,
            elevation: 16,
          }}
          {...panResponder.panHandlers}
        >
          {/* Handle */}
          <View
            style={{
              alignItems: 'center',
              paddingTop: 12,
              paddingBottom: 8,
            }}
          >
            <View
              style={{
                width: 36,
                height: 4,
                backgroundColor: theme.border,
                borderRadius: 2,
              }}
            />
          </View>

          {/* Header */}
          <View
            style={{
              flexDirection: 'row',
              alignItems: 'center',
              justifyContent: 'space-between',
              paddingHorizontal: 20,
              paddingBottom: 20,
              borderBottomWidth: 1,
              borderBottomColor: theme.border,
            }}
          >
            <TouchableOpacity onPress={handleClose} style={{ padding: 4 }}>
              <Text style={{ color: theme.primary, fontSize: 16, fontWeight: '500' }}>Cancel</Text>
            </TouchableOpacity>

            <Text
              style={{
                fontSize: 18,
                fontWeight: '600',
                color: theme.text,
              }}
            >
              Add New Task
            </Text>

            <TouchableOpacity
              onPress={handleSave}
              disabled={!title.trim()}
              style={{
                backgroundColor: title.trim() ? theme.primary : theme.border,
                paddingHorizontal: 16,
                paddingVertical: 6,
                borderRadius: 16,
              }}
            >
              <Text
                style={{
                  color: title.trim() ? '#FFFFFF' : theme.text,
                  fontSize: 16,
                  fontWeight: '600',
                }}
              >
                Save
              </Text>
            </TouchableOpacity>
          </View>

          {/* Content */}
          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{ flex: 1 }}
          >
            <ScrollView
              style={{ flex: 1 }}
              contentContainerStyle={{
                padding: 20,
                paddingBottom: insets.bottom + 20,
              }}
              showsVerticalScrollIndicator={false}
            >
              {/* Task Title */}
              <View style={{ marginBottom: 24 }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: theme.text,
                    marginBottom: 8,
                  }}
                >
                  Task Title
                </Text>
                <TextInput
                  style={{
                    backgroundColor: theme.card,
                    borderRadius: 12,
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                    fontSize: 16,
                    color: theme.text,
                    borderWidth: 1,
                    borderColor: theme.border,
                  }}
                  placeholder="Enter task title"
                  placeholderTextColor={theme.text + '60'}
                  value={title}
                  onChangeText={setTitle}
                  autoFocus
                  returnKeyType="next"
                />
              </View>

              {/* Task Description */}
              <View style={{ marginBottom: 24 }}>
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: theme.text,
                    marginBottom: 8,
                  }}
                >
                  Description (Optional)
                </Text>
                <TextInput
                  style={{
                    backgroundColor: theme.card,
                    borderRadius: 12,
                    paddingHorizontal: 16,
                    paddingVertical: 12,
                    fontSize: 16,
                    color: theme.text,
                    borderWidth: 1,
                    borderColor: theme.border,
                    height: 80,
                    textAlignVertical: 'top',
                  }}
                  placeholder="Add a description..."
                  placeholderTextColor={theme.text + '60'}
                  value={description}
                  onChangeText={setDescription}
                  multiline
                  returnKeyType="done"
                />
              </View>

              {/* Assignee Selection */}
              <View>
                <Text
                  style={{
                    fontSize: 16,
                    fontWeight: '600',
                    color: theme.text,
                    marginBottom: 12,
                  }}
                >
                  Assign To
                </Text>
                <View style={{ flexDirection: 'row', gap: 12 }}>
                  {/* Self */}
                  <TouchableOpacity
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      backgroundColor:
                        selectedAssignee === USERS.self.id ? theme.primary : theme.card,
                      borderRadius: 12,
                      padding: 12,
                      borderWidth: 1,
                      borderColor:
                        selectedAssignee === USERS.self.id ? theme.primary : theme.border,
                    }}
                    onPress={() => setSelectedAssignee(USERS.self.id)}
                  >
                    <Image
                      source={{ uri: USERS.self.avatar }}
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 16,
                        marginRight: 8,
                      }}
                    />
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: '500',
                        color: selectedAssignee === USERS.self.id ? '#FFFFFF' : theme.text,
                      }}
                    >
                      {USERS.self.name}
                    </Text>
                  </TouchableOpacity>

                  {/* Spouse */}
                  <TouchableOpacity
                    style={{
                      flex: 1,
                      flexDirection: 'row',
                      alignItems: 'center',
                      backgroundColor: selectedAssignee === spouse.id ? theme.primary : theme.card,
                      borderRadius: 12,
                      padding: 12,
                      borderWidth: 1,
                      borderColor: selectedAssignee === spouse.id ? theme.primary : theme.border,
                    }}
                    onPress={() => setSelectedAssignee(spouse.id)}
                  >
                    <Image
                      source={{ uri: spouse.avatar }}
                      style={{
                        width: 32,
                        height: 32,
                        borderRadius: 16,
                        marginRight: 8,
                      }}
                    />
                    <Text
                      style={{
                        fontSize: 14,
                        fontWeight: '500',
                        color: selectedAssignee === spouse.id ? '#FFFFFF' : theme.text,
                      }}
                    >
                      {spouse.name}
                    </Text>
                  </TouchableOpacity>
                </View>
              </View>
            </ScrollView>
          </KeyboardAvoidingView>
        </Animated.View>
      </View>
    </Modal>
  );
}
