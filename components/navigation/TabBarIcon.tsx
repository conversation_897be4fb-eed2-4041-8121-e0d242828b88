// You can explore the built-in icon families and icons on the web at https://icons.expo.fyi/

import Ionicons from '@expo/vector-icons/Ionicons';
import { type IconProps } from '@expo/vector-icons/build/createIconSet';
import { type ComponentProps } from 'react';
import { Platform } from 'react-native';

export function TabBarIcon({
  style,
  size = Platform.OS === 'ios' ? 24 : 22,
  ...rest
}: IconProps<ComponentProps<typeof Ionicons>['name']>) {
  return (
    <Ionicons
      size={size}
      style={[
        {
          marginBottom: Platform.OS === 'ios' ? -2 : 0,
          textAlign: 'center',
        },
        style,
      ]}
      {...rest}
    />
  );
}
