// CustomDrawer.tsx

import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { Drawer } from 'react-native-drawer-layout';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Slot, useRouter, useSegments } from 'expo-router';
import { useTheme } from './context/ThemeContext';

const CustomDrawer: React.FC = () => {
  const router = useRouter();
  const segments = useSegments();
  const { theme } = useTheme();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const [activeRoute, setActiveRoute] = useState('');

  // Check if we're currently in the settings screen
  const isInSettings = segments.includes('settings' as any);

  // Check if we're in the tabs (home) area - show header for tabs
  const isInTabs = segments.includes('(tabs)' as any) || segments.includes('index' as any);

  // Show header when in tabs but not in settings
  const shouldShowHeader = isInTabs && !isInSettings;

  // Drawer button component for navigation items
  const DrawerButton: React.FC<{
    title: string;
    onPress: () => void;
    isActive: boolean;
  }> = ({ title, onPress, isActive }) => {
    return (
      <TouchableOpacity
        onPress={onPress}
        hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
        style={[
          styles.drawerButton,
          isActive && styles.activeButton,
          isActive && { backgroundColor: theme.primary + '20' },
        ]}
        accessibilityLabel={title}
        accessibilityRole="button"
        activeOpacity={0.7}
      >
        <Text
          style={[
            styles.drawerButtonText,
            { color: theme.text },
            isActive && { color: theme.primary },
          ]}
        >
          {title}
        </Text>
      </TouchableOpacity>
    );
  };

  // Drawer content
  const renderDrawerContent = () => (
    <SafeAreaView
      style={[styles.drawerContainer, { backgroundColor: theme.card }]}
      edges={['top', 'bottom']}
    >
      {/* Home Button */}
      <DrawerButton
        title="Home"
        isActive={activeRoute === '(tabs)'}
        onPress={() => {
          router.push('/(tabs)');
          setActiveRoute('(tabs)');
          setDrawerOpen(false);
        }}
      />

      {/* Settings Button */}
      <DrawerButton
        title="Settings"
        isActive={activeRoute === '(drawer)/settings'}
        onPress={() => {
          router.push('/(drawer)/settings');
          setActiveRoute('(drawer)/settings');
          setDrawerOpen(false);
        }}
      />
    </SafeAreaView>
  );

  return (
    <Drawer
      open={drawerOpen}
      onOpen={() => setDrawerOpen(true)}
      onClose={() => setDrawerOpen(false)}
      drawerPosition="left"
      drawerStyle={[styles.drawerStyle, { backgroundColor: theme.card }]}
      renderDrawerContent={renderDrawerContent}
      swipeEnabled={true}
    >
      <SafeAreaView
        style={[styles.mainContainer, { backgroundColor: theme.background }]}
        edges={['top']}
      >
        {/* Header - Show when in tabs but not in settings */}
        {shouldShowHeader && (
          <View
            style={[
              styles.header,
              { backgroundColor: theme.card, borderBottomColor: theme.border },
            ]}
          >
            <TouchableOpacity
              onPress={() => setDrawerOpen(!drawerOpen)}
              accessibilityLabel="Toggle drawer"
              accessibilityRole="button"
              hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
            >
              <Ionicons name="menu" size={30} color={theme.text} />
            </TouchableOpacity>
          </View>
        )}

        {/* Main Content */}
        <Slot />
      </SafeAreaView>
    </Drawer>
  );
};

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
  },
  header: {
    padding: 10,
    flexDirection: 'row',
    justifyContent: 'flex-start',
    alignItems: 'center',
    borderBottomWidth: 1,
  },
  drawerContainer: {
    flex: 1,
    padding: 20,
  },
  drawerStyle: {
    width: 300,
  },
  drawerButton: {
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderRadius: 5,
    alignItems: 'flex-start',
    marginBottom: 15,
    backgroundColor: 'transparent',
  },
  drawerButtonText: {
    fontSize: 18,
  },
  activeButton: {
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
});

export default CustomDrawer;
