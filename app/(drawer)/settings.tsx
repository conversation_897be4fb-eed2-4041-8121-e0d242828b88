// screens/SettingsScreen.tsx

import SettingsItem from '../../components/SettingsItem';
import { USERS } from '../../constants/Users';
import React, { useState } from 'react';
import {
  View,
  SectionList,
  StyleSheet,
  TouchableOpacity,
  Text,
  TextInput,
  Modal,
  KeyboardAvoidingView,
  Platform,
  Image,
  Alert,
  Switch,
  ActivityIndicator,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Ionicons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { useTheme } from '../context/ThemeContext';

interface SettingsSection {
  title: string;
  data: string[]; // Using string identifiers for settings
}

export default function Settings() {
  const { theme, isDark, toggleTheme } = useTheme();
  const router = useRouter();
  const insets = useSafeAreaInsets();

  // Mock Data for Settings
  const [homeAddress, setHomeAddress] = useState<string>('123 Main St, Springfield');
  const [notificationsEnabled, setNotificationsEnabled] = useState<boolean>(true);
  const [profileName, setProfileName] = useState<string>(USERS.self.name);
  const [profileAvatar, setProfileAvatar] = useState<string>(USERS.self.avatar);

  // State for Modal
  const [modalVisible, setModalVisible] = useState<boolean>(false);
  const [newAddress, setNewAddress] = useState<string>('');
  const [isSettingAddress, setIsSettingAddress] = useState<boolean>(false);

  // Handle Setting Home Address
  const handleSetHomeAddress = () => {
    if (newAddress.trim()) {
      setIsSettingAddress(true);
      // Simulate setting address with a delay
      setTimeout(() => {
        setHomeAddress(newAddress.trim());
        setNewAddress('');
        setIsSettingAddress(false);
        setModalVisible(false);
        Alert.alert('Success', 'Home address has been updated.');
      }, 1000);
    } else {
      Alert.alert('Validation', 'Address cannot be empty.');
    }
  };

  // Handle Notification Toggle
  const toggleNotifications = () => {
    setNotificationsEnabled((previousState) => !previousState);
  };

  // Handle Profile Name Change
  const handleProfileNameChange = (name: string) => {
    setProfileName(name);
  };

  // Handle Logout (Mock)
  const handleLogout = () => {
    Alert.alert('Logout', 'Are you sure you want to logout?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Logout',
        style: 'destructive',
        onPress: () => {
          // Perform logout actions here
          Alert.alert('Logged Out', 'You have been logged out successfully.');
        },
      },
    ]);
  };

  // Define Settings Sections
  const sections: SettingsSection[] = [
    { title: 'Account', data: ['Profile Information'] },
    { title: 'Preferences', data: ['Theme', 'Notifications'] },
    { title: 'Location', data: ['Home Address'] },
    { title: 'Others', data: ['Logout'] },
  ];

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Header with Back Button */}
      <View
        style={[
          styles.header,
          {
            backgroundColor: theme.card,
            borderBottomColor: theme.border,
            paddingTop: insets.top + 12,
          },
        ]}
      >
        <TouchableOpacity
          onPress={() => router.back()}
          style={styles.backButton}
          accessibilityLabel="Go back"
          accessibilityRole="button"
          hitSlop={{ top: 15, bottom: 15, left: 15, right: 15 }}
        >
          <Ionicons name="arrow-back" size={24} color={theme.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: theme.text }]}>Settings</Text>
        <View style={styles.headerSpacer} />
      </View>

      {/* Section List */}
      <SectionList
        sections={sections}
        keyExtractor={(item, index) => item + index}
        renderItem={({ item }) => {
          switch (item) {
            case 'Profile Information':
              return (
                <SettingsItem
                  icon="person-circle-outline"
                  title="Profile Information"
                  description={profileName}
                  onPress={() => {}}
                  style={{ backgroundColor: theme.card }}
                  textColor={theme.text}
                />
              );
            case 'Theme':
              return (
                <SettingsItem
                  icon="color-palette-outline"
                  title="Dark Theme"
                  style={{ backgroundColor: theme.card }}
                  textColor={theme.text}
                  rightComponent={
                    <Switch
                      value={isDark}
                      onValueChange={toggleTheme}
                      thumbColor={isDark ? '#fff' : '#fff'}
                      trackColor={{ false: '#767577', true: theme.primary }}
                    />
                  }
                />
              );
            case 'Notifications':
              return (
                <SettingsItem
                  icon="notifications-outline"
                  title="Notifications"
                  style={{ backgroundColor: theme.card }}
                  textColor={theme.text}
                  rightComponent={
                    <Switch
                      value={notificationsEnabled}
                      onValueChange={toggleNotifications}
                      thumbColor={notificationsEnabled ? '#fff' : '#fff'}
                      trackColor={{ false: '#767577', true: theme.primary }}
                    />
                  }
                />
              );
            case 'Home Address':
              return (
                <SettingsItem
                  icon="home-outline"
                  title="Home Address"
                  description={homeAddress}
                  onPress={() => setModalVisible(true)}
                  style={{ backgroundColor: theme.card }}
                  textColor={theme.text}
                />
              );
            case 'Logout':
              return (
                <SettingsItem
                  icon="log-out-outline"
                  title="Logout"
                  onPress={handleLogout}
                  style={{ backgroundColor: theme.card }}
                  textColor={theme.text}
                />
              );
            default:
              return null;
          }
        }}
        renderSectionHeader={({ section: { title } }) => (
          <Text style={[styles.sectionHeader, { color: theme.text }]}>{title}</Text>
        )}
        contentContainerStyle={styles.listContent}
      />

      {/* Address Modal */}
      <Modal
        animationType="slide"
        transparent={true}
        visible={modalVisible}
        onRequestClose={() => setModalVisible(false)}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
          style={styles.modalContainer}
        >
          <View style={[styles.modalContent, { backgroundColor: theme.card }]}>
            <Text style={[styles.modalTitle, { color: theme.text }]}>Set Home Address</Text>
            <TextInput
              style={[styles.input, { color: theme.text, borderColor: theme.border }]}
              placeholder="Enter your home address"
              placeholderTextColor={theme.tabBar.inactive}
              value={newAddress}
              onChangeText={setNewAddress}
            />
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={() => setModalVisible(false)}
              >
                <Text style={styles.buttonText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[styles.button, styles.saveButton]}
                onPress={handleSetHomeAddress}
                disabled={isSettingAddress}
              >
                {isSettingAddress ? (
                  <ActivityIndicator color="#fff" />
                ) : (
                  <Text style={styles.buttonText}>Save</Text>
                )}
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 12,
    borderBottomWidth: 1,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  backButton: {
    padding: 12,
    marginRight: 8,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 44,
    minHeight: 44,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40, // Same width as back button to center the title
  },
  listContent: {
    paddingBottom: 20,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modalContent: {
    width: '80%',
    padding: 20,
    borderRadius: 10,
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
  },
  input: {
    borderWidth: 1,
    borderRadius: 5,
    padding: 10,
    marginBottom: 15,
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 5,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: '#ff3b30',
  },
  saveButton: {
    backgroundColor: '#007aff',
  },
  buttonText: {
    color: '#fff',
    fontWeight: '600',
  },
});
