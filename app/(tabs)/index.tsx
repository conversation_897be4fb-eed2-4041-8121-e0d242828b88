import { Button, Image, StyleSheet, View, Text } from 'react-native';
import { ThemedView } from '@/components/ThemedView';
import { useRouter } from 'expo-router'; // Import navigation
import React from 'react';
import { LinearGradient } from 'expo-linear-gradient'; // For gradient background

export default function HomeScreen() {
  const router = useRouter(); // Use router for navigation

  const handleGetStarted = () => {
    router.push('/todo'); // Navigate to the Todo tab
  };

  return (
    <ThemedView style={styles.container}>
      {/* Background gradient */}
      <LinearGradient
        colors={['#A1CEDC', '#f5f5f5']} // Soft gradient from top to bottom
        style={styles.background}
      >
        {/* Icon/Image at the top */}
        <View style={styles.headerContainer}>
          <Image
            source={require('@/assets/images/partial-react-logo.png')}
            style={styles.reactLogo}
          />
        </View>

        {/* Welcome text */}
        <View style={styles.contentContainer}>
          <Text style={styles.appTitle}>Welcome to Your App!</Text>
          <Text style={styles.description}>
            This app helps you manage your tasks efficiently and stay organized.
            Start by creating your first to-do!
          </Text>
          <Button title="Get Started" onPress={handleGetStarted} />
        </View>
      </LinearGradient>
    </ThemedView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  background: {
    position: 'absolute',
    left: 0,
    right: 0,
    top: 0,
    height: '100%', // Full screen height
  },
  headerContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 40,
    marginTop: 100, // Pushed towards the top for more focus
  },
  reactLogo: {
    height: 120, // Slightly smaller logo
    width: 120,
  },
  contentContainer: {
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: 20,
    paddingBottom: 30,
    backgroundColor: 'rgba(255, 255, 255, 0.85)', // Slightly transparent white box for readability
    borderRadius: 16, // Rounded corners for modern look
    marginHorizontal: 20,
    marginTop: -20, // Adjust position to blend with logo
  },
  appTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 20,
    textAlign: 'center',
  },
  description: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 30,
  },
});
