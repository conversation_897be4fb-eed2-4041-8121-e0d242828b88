// screens/AddressScreen.tsx

import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  Text,
  KeyboardAvoidingView,
  Platform,
  Dimensions,
  Alert,
  ActivityIndicator,
} from 'react-native';
import MapView, { Marker, Region, Circle } from 'react-native-maps';
import { Ionicons } from '@expo/vector-icons';
import { useTodoStore } from '@/store/todoStore'; // Zustand store
import { useLocation } from '@/hooks/useLocation'; // Custom hook for location

const { width, height } = Dimensions.get('window');

const ASPECT_RATIO = width / height;
const LATITUDE_DELTA = 0.0922;
const LONGITUDE_DELTA = LATITUDE_DELTA * ASPECT_RATIO;

// Define radii for the three zones in meters
const ZONE_RADII = [500, 1000, 1500]; // Adjust as needed

const AddressScreen: React.FC = () => {
  const { location, errorMsg, loading, fetchLocation } = useLocation();
  const [region, setRegion] = useState<Region>({
    latitude: 37.78825, // Default latitude, adjust as needed
    longitude: -122.4324, // Default longitude, adjust as needed
    latitudeDelta: LATITUDE_DELTA,
    longitudeDelta: LONGITUDE_DELTA,
  });

  const [address, setAddress] = useState('');
  const [marker, setMarker] = useState<{
    latitude: number;
    longitude: number;
  } | null>(null);

  // Zustand store actions and state
  const homeAddress = useTodoStore((state) => state.homeAddress);
  const setHomeAddress = useTodoStore((state) => state.setHomeAddress);
  const setZones = useTodoStore((state) => state.setZones);
  const zones = useTodoStore((state) => state.zones);

  const mapRef = useRef<MapView>(null);

  useEffect(() => {
    if (location) {
      const { latitude, longitude } = location.coords;
      setRegion({
        latitude,
        longitude,
        latitudeDelta: LATITUDE_DELTA,
        longitudeDelta: LONGITUDE_DELTA,
      });
      setMarker({ latitude, longitude });
      // Reverse geocode to get the address
      getAddressFromCoordinates(latitude, longitude).then((fetchedAddress) => {
        setAddress(fetchedAddress);
      });
      // Move the map to the fetched location
      mapRef.current?.animateToRegion(
        {
          latitude,
          longitude,
          latitudeDelta: LATITUDE_DELTA,
          longitudeDelta: LONGITUDE_DELTA,
        },
        1000,
      );
    }
  }, [location]);

  // Load home address and zones from the store on mount
  useEffect(() => {
    if (homeAddress) {
      setMarker({
        latitude: homeAddress.latitude,
        longitude: homeAddress.longitude,
      });
      setRegion({
        latitude: homeAddress.latitude,
        longitude: homeAddress.longitude,
        latitudeDelta: LATITUDE_DELTA,
        longitudeDelta: LONGITUDE_DELTA,
      });
      setAddress(homeAddress.address);
    }
  }, [homeAddress]);

  const handleMapPress = async (e: any) => {
    const { latitude, longitude } = e.nativeEvent.coordinate;
    setMarker({ latitude, longitude });
    // Reverse geocode to get the address
    const fetchedAddress = await getAddressFromCoordinates(latitude, longitude);
    setAddress(fetchedAddress);
  };

  const getAddressFromCoordinates = async (
    lat: number,
    lng: number,
  ): Promise<string> => {
    try {
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?latlng=${lat},${lng}&key=YOUR_GOOGLE_MAPS_API_KEY`,
      );
      const data = await response.json();
      if (data.results && data.results[0]) {
        return data.results[0].formatted_address;
      } else {
        return 'Unknown location';
      }
    } catch (error) {
      console.error(error);
      return 'Error fetching address';
    }
  };

  const handleSaveAddress = () => {
    if (marker && address) {
      setHomeAddress({
        latitude: marker.latitude,
        longitude: marker.longitude,
        address,
      });
      // Define zones based on the home address
      const definedZones = ZONE_RADII.map((radius, index) => ({
        id: `zone-${index + 1}`,
        radius,
        center: {
          latitude: marker.latitude,
          longitude: marker.longitude,
        },
      }));
      setZones(definedZones);
      Alert.alert('Success', 'Home address and zones saved successfully!');
    } else {
      Alert.alert('Error', 'Please select a location on the map.');
    }
  };

  const handleUseCurrentLocation = async () => {
    await fetchLocation(); // Fetch the current location using the hook
    if (errorMsg) {
      Alert.alert('Error', errorMsg);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
    >
      {/* Map View */}
      <MapView
        ref={mapRef}
        style={styles.map}
        region={region}
        onPress={handleMapPress}
        showsUserLocation
        showsMyLocationButton
      >
        {marker && <Marker coordinate={marker} />}
        {/* Draw zones if home address is set */}
        {zones.map((zone) => (
          <Circle
            key={zone.id}
            center={zone.center}
            radius={zone.radius}
            strokeColor="rgba(0, 122, 255, 0.5)"
            fillColor="rgba(0, 122, 255, 0.2)"
          />
        ))}
      </MapView>

      {/* Address Input and "Use Current Location" Button */}
      <View style={styles.addressContainer}>
        <View style={styles.addressInputContainer}>
          <TextInput
            style={styles.input}
            placeholder="Enter address manually"
            value={address}
            onChangeText={setAddress}
            multiline
            returnKeyType="done"
          />
          <TouchableOpacity
            style={styles.useCurrentLocationButton}
            onPress={handleUseCurrentLocation}
            accessibilityLabel="Use current location"
            accessibilityRole="button"
            activeOpacity={0.7}
          >
            {loading ? (
              <ActivityIndicator color="#fff" size="small" />
            ) : (
              <Ionicons name="locate" size={14} color="#fff" />
            )}
          </TouchableOpacity>
        </View>
        <TouchableOpacity
          style={[
            styles.saveButton,
            { backgroundColor: address.trim() ? '#007AFF' : '#a0c4ff' },
          ]}
          onPress={handleSaveAddress}
          disabled={!address.trim()}
          accessibilityLabel="Save address"
          accessibilityRole="button"
          activeOpacity={0.7}
        >
          <Ionicons name="save" size={20} color="#fff" />
          <Text style={styles.saveButtonText}>Save Address</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
};

export default AddressScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  map: {
    flex: 1,
  },
  addressContainer: {
    position: 'absolute',
    bottom: 20,
    left: 20,
    right: 20,
    backgroundColor: '#fff', // Matching the "add modal" background
    borderRadius: 12, // Rounded corners similar to modal
    padding: 20, // Adequate padding for inner components
    shadowColor: '#000',
    shadowOpacity: 0.2,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
    elevation: 5, // Android shadow
  },
  addressInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    position: 'relative',
  },
  input: {
    flex: 1,
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    fontSize: 16,
    backgroundColor: '#f9f9f9', // Light background similar to modal inputs
    paddingRight: 40, // Space for the button
    maxHeight: 100, // Prevent excessive height for multiline
  },
  useCurrentLocationButton: {
    position: 'absolute',
    right: 10,
    padding: 6,
    backgroundColor: '#007AFF',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    height: 50,
    borderRadius: 8,
    marginTop: 15, // Space between input and save button
  },
  saveButtonText: {
    color: '#fff',
    fontSize: 16,
    marginLeft: 8,
    fontWeight: '500',
  },
});
