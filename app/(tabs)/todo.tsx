// screens/TodoScreen.tsx

import React, { useState } from 'react';
import {
  View,
  SectionList,
  StyleSheet,
  TouchableOpacity,
  Text,
  Platform,
  Alert,
} from 'react-native';
import { USERS } from '@/constants/Users';
import { Ionicons } from '@expo/vector-icons';
import { router } from 'expo-router';
import TodoItem from '@/components/todo/TodoItem';
import { useTheme } from '../context/ThemeContext';

interface Todo {
  id: string;
  title: string;
  description?: string;
  assigneeId: string;
  completed: boolean;
}

const TodoScreen: React.FC = () => {
  const { theme } = useTheme();
  const spouse = USERS.others[0]; // Using the first person from others as spouse

  // Mock Data for Todos
  const [todos, setTodos] = useState<Todo[]>([
    {
      id: '1',
      title: 'Buy groceries',
      description: 'Milk, Eggs, Bread',
      assigneeId: USERS.self.id,
      completed: false,
    },
    {
      id: '2',
      title: 'Clean the house',
      description: 'Living room and kitchen',
      assigneeId: spouse.id,
      completed: false,
    },
    {
      id: '3',
      title: 'Prepare dinner',
      description: 'Grilled chicken with vegetables',
      assigneeId: USERS.self.id,
      completed: true,
    },
    {
      id: '4',
      title: 'Pay bills',
      description: 'Electricity and internet',
      assigneeId: spouse.id,
      completed: false,
    },
  ]);

  // State for Modal
  // Removed modal state - now using navigation

  // Handle Toggling Completion
  const handleToggleTodo = (id: string) => {
    const updatedTodos = todos.map((todo) =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo,
    );
    updatedTodos.sort((a, b) => Number(a.completed) - Number(b.completed));
    setTodos(updatedTodos);
  };

  // Handle Deleting a Todo
  const handleDeleteTodo = (id: string) => {
    Alert.alert('Confirm Delete', 'Are you sure you want to delete this task?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: () => {
          const updatedTodos = todos.filter((todo) => todo.id !== id);
          setTodos(updatedTodos);
        },
      },
    ]);
  };

  // Prepare Sections for SectionList
  const myTasks = todos.filter((todo) => todo.assigneeId === USERS.self.id);
  const spouseTasks = todos.filter((todo) => todo.assigneeId === spouse.id);

  // Create sections only if there are tasks
  const sections = [];
  if (myTasks.length > 0) {
    sections.push({ title: 'My Tasks', data: myTasks });
  }
  if (spouseTasks.length > 0) {
    sections.push({ title: `${spouse.name}'s Tasks`, data: spouseTasks });
  }

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Task List */}
      <SectionList
        sections={sections}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TodoItem
            todo={item}
            onToggle={() => handleToggleTodo(item.id)}
            onDelete={() => handleDeleteTodo(item.id)}
          />
        )}
        renderSectionHeader={({ section: { title } }) => (
          <View style={styles.sectionHeaderContainer}>
            <Text style={[styles.sectionHeader, { color: theme.text }]}>{title}</Text>
          </View>
        )}
        contentContainerStyle={styles.listContent}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Ionicons
              name="checkmark-circle-outline"
              size={64}
              color={theme.text}
              style={styles.emptyIcon}
            />
            <Text style={[styles.emptyTitle, { color: theme.text }]}>All caught up!</Text>
            <Text style={[styles.emptyText, { color: theme.text }]}>
              No tasks available. Tap the + button to add a new task.
            </Text>
          </View>
        }
      />

      {/* Add Todo Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => router.push('/modals/add-task')}
        accessibilityLabel="Add new task"
        accessibilityRole="button"
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>
    </View>
  );
};

export default TodoScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
  },
  sectionHeaderContainer: {
    backgroundColor: 'transparent',
    paddingHorizontal: 20,
    paddingTop: 24,
    paddingBottom: 8,
  },
  sectionHeader: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    opacity: 0.7,
  },
  listContent: {
    paddingBottom: Platform.OS === 'ios' ? 120 : 100,
    paddingTop: 8,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingTop: 80,
  },
  emptyIcon: {
    opacity: 0.3,
    marginBottom: 16,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginBottom: 8,
    textAlign: 'center',
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    lineHeight: 22,
  },
  addButton: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 100 : 80,
    right: 20,
    backgroundColor: '#007AFF',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8,
    shadowColor: '#000',
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    zIndex: 1000,
  },
});
