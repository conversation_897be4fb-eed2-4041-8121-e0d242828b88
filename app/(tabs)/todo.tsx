// screens/TodoScreen.tsx

import React, { useState } from 'react';
import { View, SectionList, StyleSheet, TouchableOpacity, Text, Platform } from 'react-native';
import { USERS } from '@/constants/Users';
import { Ionicons } from '@expo/vector-icons';
import TodoItem from '@/components/todo/TodoItem';
import { useTheme } from '../context/ThemeContext';
import AddTaskBottomSheet from '@/components/modals/AddTaskBottomSheet';

interface Todo {
  id: string;
  title: string;
  description?: string;
  assigneeId: string;
  completed: boolean;
}

const TodoScreen: React.FC = () => {
  const { theme } = useTheme();
  const spouse = USERS.others[0]; // Using the first person from others as spouse

  // Mock Data for Todos
  const [todos, setTodos] = useState<Todo[]>([
    {
      id: '1',
      title: 'Buy groceries',
      description: 'Milk, Eggs, Bread',
      assigneeId: USERS.self.id,
      completed: false,
    },
    {
      id: '2',
      title: 'Clean the house',
      description: 'Living room and kitchen',
      assigneeId: spouse.id,
      completed: false,
    },
    {
      id: '3',
      title: 'Prepare dinner',
      description: 'Grilled chicken with vegetables',
      assigneeId: USERS.self.id,
      completed: true,
    },
    {
      id: '4',
      title: 'Pay bills',
      description: 'Electricity and internet',
      assigneeId: spouse.id,
      completed: false,
    },
  ]);

  // State for Modal
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [newTodoTitle, setNewTodoTitle] = useState('');
  const [selectedAssignee, setSelectedAssignee] = useState<string>(USERS.self.id);
  const [newTodoDescription, setNewTodoDescription] = useState('');

  // Handle Adding a New Todo
  const handleAddTodo = (title: string, description: string, assigneeId: string) => {
    const newTodo: Todo = {
      id: Date.now().toString(),
      title,
      description,
      assigneeId,
      completed: false,
    };
    setTodos([...todos, newTodo]);
    setNewTodoTitle('');
    setNewTodoDescription('');
    setSelectedAssignee(USERS.self.id);
  };

  // Handle closing modal
  const handleCloseModal = () => {
    setAddModalVisible(false);
    setNewTodoTitle('');
    setNewTodoDescription('');
    setSelectedAssignee(USERS.self.id);
  };

  // Handle Toggling Completion
  const handleToggleTodo = (id: string) => {
    const updatedTodos = todos.map((todo) =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo,
    );
    updatedTodos.sort((a, b) => Number(a.completed) - Number(b.completed));
    setTodos(updatedTodos);
  };

  // Handle Deleting a Todo
  const handleDeleteTodo = (id: string) => {
    Alert.alert('Confirm Delete', 'Are you sure you want to delete this task?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: () => {
          const updatedTodos = todos.filter((todo) => todo.id !== id);
          setTodos(updatedTodos);
        },
      },
    ]);
  };

  // Prepare Sections for SectionList
  const myTasks = todos.filter((todo) => todo.assigneeId === USERS.self.id);
  const spouseTasks = todos.filter((todo) => todo.assigneeId === spouse.id);

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Task List */}
      <SectionList
        sections={[
          { title: 'My Tasks', data: myTasks },
          { title: `${spouse.name}'s Tasks`, data: spouseTasks },
        ]}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TodoItem
            todo={item}
            onToggle={() => handleToggleTodo(item.id)}
            onDelete={() => handleDeleteTodo(item.id)}
          />
        )}
        renderSectionHeader={({ section: { title } }) => (
          <Text style={[styles.sectionHeader, { color: theme.text }]}>{title}</Text>
        )}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: theme.text }]}>
              No tasks available. Add a new task!
            </Text>
          </View>
        }
      />

      {/* Add Todo Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setAddModalVisible(true)}
        accessibilityLabel="Add new task"
        accessibilityRole="button"
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>

      {/* Add Todo Bottom Sheet */}
      <AddTaskBottomSheet
        visible={addModalVisible}
        onClose={handleCloseModal}
        onSave={handleAddTodo}
        title={newTodoTitle}
        setTitle={setNewTodoTitle}
        description={newTodoDescription}
        setDescription={setNewTodoDescription}
        selectedAssignee={selectedAssignee}
        setSelectedAssignee={setSelectedAssignee}
      />
    </View>
  );
};

export default TodoScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 10,
  },
  sectionHeader: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  listContent: {
    paddingBottom: Platform.OS === 'ios' ? 120 : 100, // To prevent content from being hidden behind the add button
  },
  emptyContainer: {
    alignItems: 'center',
    marginTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
  addButton: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 100 : 80,
    right: 20,
    backgroundColor: '#007AFF',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    zIndex: 1000,
  },
});
