// screens/TodoScreen.tsx

import React, { useState } from 'react';
import {
  View,
  SectionList,
  StyleSheet,
  TouchableOpacity,
  Text,
  TextInput,
  Modal,
  KeyboardAvoidingView,
  Platform,
  Image,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { USERS } from '@/constants/Users';
import { Ionicons } from '@expo/vector-icons';
import TodoItem from '@/components/todo/TodoItem';
import { useTheme } from '../context/ThemeContext';
import AddTaskModal from '@/components/todo/AddTaskModal';

interface Todo {
  id: string;
  title: string;
  description?: string;
  assigneeId: string;
  completed: boolean;
}

const TodoScreen: React.FC = () => {
  const { theme } = useTheme();
  const spouse = USERS.others[0]; // Using the first person from others as spouse

  // Mock Data for Todos
  const [todos, setTodos] = useState<Todo[]>([
    {
      id: '1',
      title: 'Buy groceries',
      description: 'Milk, Eggs, Bread',
      assigneeId: USERS.self.id,
      completed: false,
    },
    {
      id: '2',
      title: 'Clean the house',
      description: 'Living room and kitchen',
      assigneeId: spouse.id,
      completed: false,
    },
    {
      id: '3',
      title: 'Prepare dinner',
      description: 'Grilled chicken with vegetables',
      assigneeId: USERS.self.id,
      completed: true,
    },
    {
      id: '4',
      title: 'Pay bills',
      description: 'Electricity and internet',
      assigneeId: spouse.id,
      completed: false,
    },
  ]);

  // State for Modal
  const [addModalVisible, setAddModalVisible] = useState(false);
  const [newTodoTitle, setNewTodoTitle] = useState('');
  const [selectedAssignee, setSelectedAssignee] = useState<string>(USERS.self.id);
  const [newTodoDescription, setNewTodoDescription] = useState('');

  // Handle Adding a New Todo
  const handleAddTodo = () => {
    if (newTodoTitle.trim()) {
      const newTodo: Todo = {
        id: Date.now().toString(),
        title: newTodoTitle,
        description: newTodoDescription,
        assigneeId: selectedAssignee,
        completed: false,
      };
      setTodos([...todos, newTodo]);
      setNewTodoTitle('');
      setNewTodoDescription('');
      setSelectedAssignee(USERS.self.id);
      setAddModalVisible(false);
    } else {
      Alert.alert('Validation', 'Task title cannot be empty.');
    }
  };

  // Handle Toggling Completion
  const handleToggleTodo = (id: string) => {
    const updatedTodos = todos.map((todo) =>
      todo.id === id ? { ...todo, completed: !todo.completed } : todo,
    );
    updatedTodos.sort((a, b) => Number(a.completed) - Number(b.completed));
    setTodos(updatedTodos);
  };

  // Handle Deleting a Todo
  const handleDeleteTodo = (id: string) => {
    Alert.alert('Confirm Delete', 'Are you sure you want to delete this task?', [
      { text: 'Cancel', style: 'cancel' },
      {
        text: 'Delete',
        style: 'destructive',
        onPress: () => {
          const updatedTodos = todos.filter((todo) => todo.id !== id);
          setTodos(updatedTodos);
        },
      },
    ]);
  };

  // Prepare Sections for SectionList
  const myTasks = todos.filter((todo) => todo.assigneeId === USERS.self.id);
  const spouseTasks = todos.filter((todo) => todo.assigneeId === spouse.id);

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      {/* Task List */}
      <SectionList
        sections={[
          { title: 'My Tasks', data: myTasks },
          { title: `${spouse.name}'s Tasks`, data: spouseTasks },
        ]}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TodoItem
            todo={item}
            onToggle={() => handleToggleTodo(item.id)}
            onDelete={() => handleDeleteTodo(item.id)}
          />
        )}
        renderSectionHeader={({ section: { title } }) => (
          <Text style={[styles.sectionHeader, { color: theme.text }]}>{title}</Text>
        )}
        contentContainerStyle={styles.listContent}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <Text style={[styles.emptyText, { color: theme.text }]}>
              No tasks available. Add a new task!
            </Text>
          </View>
        }
      />

      {/* Add Todo Button */}
      <TouchableOpacity
        style={styles.addButton}
        onPress={() => setAddModalVisible(true)}
        accessibilityLabel="Add new task"
        accessibilityRole="button"
      >
        <Ionicons name="add" size={24} color="#fff" />
      </TouchableOpacity>

      {/* Add Todo Modal */}
      <Modal
        visible={addModalVisible}
        animationType="slide"
        transparent={true}
        onRequestClose={() => setAddModalVisible(false)}
      >
        <KeyboardAvoidingView
          behavior={Platform.OS === 'ios' ? 'padding' : undefined}
          style={styles.modalContainer}
        >
          <View style={styles.modalContent}>
            <Text style={styles.modalTitle}>Add New Task</Text>
            <TextInput
              style={styles.input}
              placeholder="Task Title"
              value={newTodoTitle}
              onChangeText={setNewTodoTitle}
              returnKeyType="done"
            />
            <TextInput
              style={[styles.input, styles.descriptionInput]}
              placeholder="Task Description (Optional)"
              value={newTodoDescription}
              onChangeText={setNewTodoDescription}
              multiline
            />
            {/* Assignee Selection */}
            <View style={styles.assigneeContainer}>
              <Text style={styles.assigneeLabel}>Assign To:</Text>
              <TouchableOpacity
                style={[
                  styles.assigneeButton,
                  selectedAssignee === USERS.self.id && styles.assigneeButtonSelected,
                ]}
                onPress={() => setSelectedAssignee(USERS.self.id)}
                accessibilityLabel="Assign to yourself"
                accessibilityRole="button"
              >
                <Image source={{ uri: USERS.self.avatar }} style={styles.assigneeAvatar} />
                <Text
                  style={[
                    styles.assigneeText,
                    selectedAssignee === USERS.self.id && styles.assigneeTextSelected,
                  ]}
                >
                  {USERS.self.name}
                </Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.assigneeButton,
                  selectedAssignee === spouse.id && styles.assigneeButtonSelected,
                ]}
                onPress={() => setSelectedAssignee(spouse.id)}
                accessibilityLabel="Assign to your spouse"
                accessibilityRole="button"
              >
                <Image source={{ uri: spouse.avatar }} style={styles.assigneeAvatar} />
                <Text
                  style={[
                    styles.assigneeText,
                    selectedAssignee === spouse.id && styles.assigneeTextSelected,
                  ]}
                >
                  {spouse.name}
                </Text>
              </TouchableOpacity>
            </View>
            {/* Modal Buttons */}
            <View style={styles.modalButtons}>
              <TouchableOpacity
                style={[styles.button, styles.cancelButton]}
                onPress={() => setAddModalVisible(false)}
                accessibilityLabel="Cancel adding task"
                accessibilityRole="button"
              >
                <Text style={styles.cancelText}>Cancel</Text>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.button,
                  styles.saveButton,
                  {
                    backgroundColor: newTodoTitle.trim() ? '#007AFF' : '#a0c4ff',
                  },
                ]}
                onPress={handleAddTodo}
                disabled={!newTodoTitle.trim()}
                accessibilityLabel="Save new task"
                accessibilityRole="button"
                activeOpacity={0.7}
              >
                <Text style={styles.buttonText}>Save</Text>
              </TouchableOpacity>
            </View>
          </View>
        </KeyboardAvoidingView>
      </Modal>
    </View>
  );
};

export default TodoScreen;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F5F5',
    padding: 10,
  },
  sectionHeader: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
    marginTop: 20,
    marginBottom: 10,
    paddingHorizontal: 20,
  },
  listContent: {
    paddingBottom: Platform.OS === 'ios' ? 120 : 100, // To prevent content from being hidden behind the add button
  },
  emptyContainer: {
    alignItems: 'center',
    marginTop: 50,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
  },
  addButton: {
    position: 'absolute',
    bottom: Platform.OS === 'ios' ? 100 : 80,
    right: 20,
    backgroundColor: '#007AFF',
    width: 56,
    height: 56,
    borderRadius: 28,
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 8, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOpacity: 0.25,
    shadowOffset: { width: 0, height: 4 },
    shadowRadius: 8,
    zIndex: 1000,
  },
  modalContainer: {
    flex: 1,
    justifyContent: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)', // Semi-transparent background
    padding: 20,
  },
  modalContent: {
    backgroundColor: '#fff',
    borderRadius: 12,
    padding: 20,
    elevation: 5, // Android shadow
    shadowColor: '#000', // iOS shadow
    shadowOpacity: 0.3,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    color: '#333',
  },
  input: {
    borderColor: '#ddd',
    borderWidth: 1,
    borderRadius: 8,
    paddingHorizontal: 10,
    fontSize: 16,
    backgroundColor: '#f9f9f9',
    marginBottom: 15,
    height: 40,
  },
  descriptionInput: {
    height: 80,
    textAlignVertical: 'top',
  },
  assigneeContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  assigneeLabel: {
    fontSize: 16,
    marginRight: 10,
    color: '#333',
  },
  assigneeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 8,
    borderColor: '#007AFF',
    borderWidth: 1,
    borderRadius: 8,
    marginRight: 10,
  },
  assigneeButtonSelected: {
    backgroundColor: '#007AFF',
  },
  assigneeAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
  },
  assigneeText: {
    fontSize: 14,
    color: '#007AFF',
  },
  assigneeTextSelected: {
    color: '#FFFFFF',
  },
  assigneeButtonText: {
    fontSize: 14,
    color: '#007AFF',
  },
  modalButtons: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
  },
  button: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
    marginLeft: 10,
  },
  cancelButton: {
    backgroundColor: '#FFFFFF',
    borderColor: '#4A4A4A',
    borderWidth: 1,
  },
  cancelText: {
    color: '#4A4A4A',
  },
  saveButton: {
    backgroundColor: '#007AFF',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  errorText: {
    color: '#FF3B30',
    fontSize: 16,
    marginBottom: 10,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: '#007AFF',
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
  },
});
