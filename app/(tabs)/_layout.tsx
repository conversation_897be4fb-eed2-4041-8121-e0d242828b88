// app/(tabs)/_layout.tsx

import React from 'react';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Platform, StyleSheet, View } from 'react-native';
import { ROUTES } from '../constants/routes';
import { useTheme } from '../context/ThemeContext';
import { StatusBar } from 'expo-status-bar';

// Type for tab icons mapping
type TabIconName = {
  [key: string]: {
    active: keyof typeof Ionicons.glyphMap;
    inactive: keyof typeof Ionicons.glyphMap;
  };
};

const TAB_ICONS: TabIconName = {
  index: {
    active: 'home',
    inactive: 'home-outline',
  },
  address: {
    active: 'location',
    inactive: 'location-outline',
  },
  todo: {
    active: 'list',
    inactive: 'list-outline',
  },
};

export default function TabLayout() {
  const { theme, isDark } = useTheme();

  return (
    <>
      <StatusBar style={theme.statusBar} />
      <Tabs
        screenOptions={({ route }) => ({
          headerShown: false,
          tabBarActiveTintColor: theme.tabBar.active,
          tabBarInactiveTintColor: theme.tabBar.inactive,
          tabBarStyle: [
            styles.tabBarStyle,
            {
              backgroundColor: theme.tabBar.background,
              borderTopColor: theme.tabBar.border,
            },
          ],
          tabBarLabelStyle: styles.tabBarLabelStyle,
          tabBarAllowFontScaling: true,
          tabBarIcon: ({ focused, color, size }) => {
            const iconConfig = TAB_ICONS[route.name];
            if (!iconConfig) return null;

            const iconName = focused ? iconConfig.active : iconConfig.inactive;

            return (
              <View style={[styles.iconContainer, focused && styles.activeIconContainer]}>
                <Ionicons name={iconName} size={24} color={color} style={styles.icon} />
              </View>
            );
          },
        })}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: 'Home',
            href: ROUTES.TABS.HOME,
          }}
        />
        <Tabs.Screen
          name="address"
          options={{
            title: 'Address',
            href: ROUTES.TABS.ADDRESS,
          }}
        />
        <Tabs.Screen
          name="todo"
          options={{
            title: 'Todo',
            href: ROUTES.TABS.TODO,
          }}
        />
      </Tabs>
    </>
  );
}

const styles = StyleSheet.create({
  iconContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingTop: 8,
  },
  activeIconContainer: {
    transform: [{ scale: 1.1 }],
  },
  icon: {
    marginBottom: 4,
  },
  tabBarStyle: {
    height: Platform.OS === 'ios' ? 88 : 60,
    paddingTop: 8,
    paddingBottom: Platform.OS === 'ios' ? 28 : 8,
    borderTopWidth: 0.5,
    elevation: 0,
    shadowOpacity: 0,
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  tabBarLabelStyle: {
    fontSize: 11,
    fontWeight: '500',
    marginTop: 0,
    marginBottom: Platform.OS === 'ios' ? 6 : 4,
  },
});
