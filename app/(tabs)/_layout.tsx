// app/(tabs)/_layout.tsx

import React, { useState } from 'react';
import { Tabs, router, useSegments } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Platform, StyleSheet, View, TouchableOpacity } from 'react-native';
import { ROUTES } from '../../constants/routes';
import { useTheme } from '../context/ThemeContext';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Drawer } from 'react-native-drawer-layout';

// Type for tab icons mapping
type TabIconName = {
  [key: string]: {
    active: keyof typeof Ionicons.glyphMap;
    inactive: keyof typeof Ionicons.glyphMap;
  };
};

const TAB_ICONS: TabIconName = {
  index: {
    active: 'home',
    inactive: 'home-outline',
  },
  address: {
    active: 'location',
    inactive: 'location-outline',
  },
  todo: {
    active: 'checkmark-circle',
    inactive: 'checkmark-circle-outline',
  },
};

export default function TabLayout() {
  const { theme, isDark } = useTheme();
  const [drawerOpen, setDrawerOpen] = useState(false);
  const segments = useSegments();

  // All tabs now use Expo Router headers, so no custom header logic needed

  // Drawer content
  const renderDrawerContent = () => (
    <SafeAreaView
      style={[styles.drawerContainer, { backgroundColor: theme.card }]}
      edges={['top', 'bottom']}
    >
      {/* Drawer Header */}
      <View style={styles.drawerHeader}>
        <Text style={[styles.drawerHeaderText, { color: theme.text }]}>Menu</Text>
      </View>

      {/* Home Button */}
      <TouchableOpacity
        style={[styles.drawerButton, { backgroundColor: 'transparent' }]}
        onPress={() => {
          router.push('/(tabs)');
          setDrawerOpen(false);
        }}
      >
        <Text style={[styles.drawerButtonText, { color: theme.text }]}>Home</Text>
      </TouchableOpacity>

      {/* Settings Button */}
      <TouchableOpacity
        style={[styles.drawerButton, { backgroundColor: 'transparent' }]}
        onPress={() => {
          router.push('/(drawer)/settings');
          setDrawerOpen(false);
        }}
      >
        <Text style={[styles.drawerButtonText, { color: theme.text }]}>Settings</Text>
      </TouchableOpacity>
    </SafeAreaView>
  );

  return (
    <Drawer
      open={drawerOpen}
      onOpen={() => setDrawerOpen(true)}
      onClose={() => setDrawerOpen(false)}
      drawerPosition="left"
      drawerStyle={[styles.drawerStyle, { backgroundColor: theme.card }]}
      renderDrawerContent={renderDrawerContent}
      swipeEnabled={true}
    >
      <SafeAreaView style={{ flex: 1, backgroundColor: theme.background }} edges={[]}>
        <StatusBar style={theme.statusBar} />
        <Tabs
          screenOptions={({ route }) => ({
            headerShown: false,
            tabBarActiveTintColor: theme.tabBar.active,
            tabBarInactiveTintColor: theme.tabBar.inactive,
            tabBarStyle: [
              styles.tabBarStyle,
              {
                backgroundColor: theme.tabBar.background,
                borderTopColor: theme.tabBar.border,
              },
            ],
            tabBarLabelStyle: styles.tabBarLabelStyle,
            tabBarAllowFontScaling: false,
            tabBarHideOnKeyboard: Platform.OS === 'android',
            tabBarItemStyle: styles.tabBarItemStyle,
            tabBarIcon: ({ focused, color, size }) => {
              const iconConfig = TAB_ICONS[route.name];
              if (!iconConfig) return null;

              const iconName = focused ? iconConfig.active : iconConfig.inactive;
              const iconSize = Platform.OS === 'ios' ? 24 : 22;

              return (
                <View style={[styles.iconContainer, focused && styles.activeIconContainer]}>
                  <Ionicons
                    name={iconName}
                    size={iconSize}
                    color={color}
                    style={[
                      styles.icon,
                      {
                        // Ensure consistent icon alignment
                        lineHeight: iconSize,
                      },
                    ]}
                  />
                </View>
              );
            },
          })}
        >
          <Tabs.Screen
            name="index"
            options={{
              title: 'Nearest & Dearest',
              href: ROUTES.TABS.HOME,
              headerShown: true,
              headerStyle: {
                backgroundColor: theme.card,
                borderBottomWidth: 1,
                borderBottomColor: theme.border,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 4,
                height: 60,
              },
              headerTintColor: theme.text,
              headerTitleStyle: {
                fontWeight: '700',
                fontSize: 20,
              },
              headerLeft: () => (
                <TouchableOpacity
                  style={{ marginLeft: 16, padding: 8 }}
                  onPress={() => setDrawerOpen(!drawerOpen)}
                  accessibilityLabel="Toggle drawer"
                  accessibilityRole="button"
                >
                  <Ionicons name="menu" size={24} color={theme.text} />
                </TouchableOpacity>
              ),
            }}
          />
          <Tabs.Screen
            name="address"
            options={{
              title: 'Address',
              href: ROUTES.TABS.ADDRESS,
              headerShown: true,
              headerStyle: {
                backgroundColor: theme.card,
                borderBottomWidth: 1,
                borderBottomColor: theme.border,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 4,
                height: 60,
              },
              headerTintColor: theme.text,
              headerTitleStyle: {
                fontWeight: '700',
                fontSize: 20,
              },
              headerLeft: () => (
                <TouchableOpacity
                  style={{ marginLeft: 16, padding: 8 }}
                  onPress={() => setDrawerOpen(!drawerOpen)}
                  accessibilityLabel="Toggle drawer"
                  accessibilityRole="button"
                >
                  <Ionicons name="menu" size={24} color={theme.text} />
                </TouchableOpacity>
              ),
            }}
          />
          <Tabs.Screen
            name="todo"
            options={{
              title: 'Tasks',
              href: ROUTES.TABS.TODO,
              headerShown: true,
              headerStyle: {
                backgroundColor: theme.card,
                borderBottomWidth: 1,
                borderBottomColor: theme.border,
                shadowColor: '#000',
                shadowOffset: { width: 0, height: 2 },
                shadowOpacity: 0.1,
                shadowRadius: 4,
                elevation: 4,
                height: 60,
              },
              headerTintColor: theme.text,
              headerTitleStyle: {
                fontWeight: '700',
                fontSize: 20,
              },
              headerLeft: () => (
                <TouchableOpacity
                  style={{ marginLeft: 16, padding: 8 }}
                  onPress={() => setDrawerOpen(!drawerOpen)}
                  accessibilityLabel="Toggle drawer"
                  accessibilityRole="button"
                >
                  <Ionicons name="menu" size={24} color={theme.text} />
                </TouchableOpacity>
              ),
              headerRight: () => (
                <TouchableOpacity
                  style={{ marginRight: 16, padding: 8 }}
                  onPress={() => {
                    // Add filter/menu functionality later
                  }}
                >
                  <Ionicons name="ellipsis-horizontal" size={20} color={theme.text} />
                </TouchableOpacity>
              ),
            }}
          />
        </Tabs>
      </SafeAreaView>
    </Drawer>
  );
}

const styles = StyleSheet.create({
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 28,
    height: 28,
    marginBottom: Platform.OS === 'ios' ? 1 : 0,
  },
  activeIconContainer: {
    transform: [{ scale: 1.02 }],
  },
  icon: {
    textAlign: 'center',
  },
  tabBarStyle: {
    height: Platform.OS === 'ios' ? 84 : 60,
    paddingTop: Platform.OS === 'ios' ? 6 : 8,
    paddingBottom: Platform.OS === 'ios' ? 20 : 8,
    paddingHorizontal: 4,
    borderTopWidth: 1,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabBarLabelStyle: {
    fontSize: Platform.OS === 'ios' ? 10 : 9,
    fontWeight: '600',
    marginTop: Platform.OS === 'ios' ? 1 : 2,
    marginBottom: Platform.OS === 'ios' ? 0 : 1,
    textAlign: 'center',
  },
  tabBarItemStyle: {
    paddingVertical: Platform.OS === 'ios' ? 2 : 4,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },

  drawerContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  drawerHeader: {
    paddingVertical: 20,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
    marginBottom: 20,
  },
  drawerHeaderText: {
    fontSize: 24,
    fontWeight: 'bold',
  },
  drawerStyle: {
    width: 300,
  },
  drawerButton: {
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderRadius: 5,
    alignItems: 'flex-start',
    marginBottom: 15,
  },
  drawerButtonText: {
    fontSize: 18,
  },
});
