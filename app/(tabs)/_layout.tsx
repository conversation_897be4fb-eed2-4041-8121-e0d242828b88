// app/(tabs)/_layout.tsx

import React from 'react';
import { Tabs } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { Platform, StyleSheet, View } from 'react-native';
import { ROUTES } from '@/constants/routes';
import { useTheme } from '../context/ThemeContext';
import { StatusBar } from 'expo-status-bar';

// Type for tab icons mapping
type TabIconName = {
  [key: string]: {
    active: keyof typeof Ionicons.glyphMap;
    inactive: keyof typeof Ionicons.glyphMap;
  };
};

const TAB_ICONS: TabIconName = {
  index: {
    active: 'home',
    inactive: 'home-outline',
  },
  address: {
    active: 'location',
    inactive: 'location-outline',
  },
  todo: {
    active: 'checkmark-circle',
    inactive: 'checkmark-circle-outline',
  },
};

export default function TabLayout() {
  const { theme, isDark } = useTheme();

  return (
    <>
      <StatusBar style={theme.statusBar} />
      <Tabs
        screenOptions={({ route }) => ({
          headerShown: false,
          tabBarActiveTintColor: theme.tabBar.active,
          tabBarInactiveTintColor: theme.tabBar.inactive,
          tabBarStyle: [
            styles.tabBarStyle,
            {
              backgroundColor: theme.tabBar.background,
              borderTopColor: theme.tabBar.border,
            },
          ],
          tabBarLabelStyle: styles.tabBarLabelStyle,
          tabBarAllowFontScaling: false,
          tabBarHideOnKeyboard: Platform.OS === 'android',
          tabBarItemStyle: styles.tabBarItemStyle,
          tabBarIcon: ({ focused, color, size }) => {
            const iconConfig = TAB_ICONS[route.name];
            if (!iconConfig) return null;

            const iconName = focused ? iconConfig.active : iconConfig.inactive;
            const iconSize = Platform.OS === 'ios' ? 24 : 22;

            return (
              <View style={[styles.iconContainer, focused && styles.activeIconContainer]}>
                <Ionicons
                  name={iconName}
                  size={iconSize}
                  color={color}
                  style={[
                    styles.icon,
                    {
                      // Ensure consistent icon alignment
                      lineHeight: iconSize,
                    },
                  ]}
                />
              </View>
            );
          },
        })}
      >
        <Tabs.Screen
          name="index"
          options={{
            title: 'Home',
            href: ROUTES.TABS.HOME,
          }}
        />
        <Tabs.Screen
          name="address"
          options={{
            title: 'Address',
            href: ROUTES.TABS.ADDRESS,
          }}
        />
        <Tabs.Screen
          name="todo"
          options={{
            title: 'My Tasks',
            href: ROUTES.TABS.TODO,
            headerShown: true,
            headerStyle: { backgroundColor: theme.background },
            headerTintColor: theme.text,
            headerTitleStyle: { fontWeight: '600', fontSize: 18 },
          }}
        />
      </Tabs>
    </>
  );
}

const styles = StyleSheet.create({
  iconContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    width: 28,
    height: 28,
    marginBottom: Platform.OS === 'ios' ? 1 : 0,
  },
  activeIconContainer: {
    transform: [{ scale: 1.02 }],
  },
  icon: {
    textAlign: 'center',
  },
  tabBarStyle: {
    height: Platform.OS === 'ios' ? 84 : 60,
    paddingTop: Platform.OS === 'ios' ? 6 : 8,
    paddingBottom: Platform.OS === 'ios' ? 20 : 8,
    paddingHorizontal: 4,
    borderTopWidth: 1,
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  tabBarLabelStyle: {
    fontSize: Platform.OS === 'ios' ? 10 : 9,
    fontWeight: '600',
    marginTop: Platform.OS === 'ios' ? 1 : 2,
    marginBottom: Platform.OS === 'ios' ? 0 : 1,
    textAlign: 'center',
  },
  tabBarItemStyle: {
    paddingVertical: Platform.OS === 'ios' ? 2 : 4,
    justifyContent: 'center',
    alignItems: 'center',
    flex: 1,
  },
});
