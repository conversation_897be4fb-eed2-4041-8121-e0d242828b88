export const lightTheme = {
  background: '#FFFFFF',
  text: '#000000',
  primary: '#007AFF',
  secondary: '#5856D6',
  border: '#E5E5EA',
  card: '#FFFFFF',
  error: '#FF3B30',
  tabBar: {
    active: '#007AFF',
    inactive: '#8E8E93',
    background: '#FFFFFF',
    border: '#E5E5EA',
  },
  statusBar: 'dark',
} as const;

export const darkTheme = {
  background: '#000000',
  text: '#FFFFFF',
  primary: '#0A84FF',
  secondary: '#5E5CE6',
  border: '#38383A',
  card: '#1C1C1E',
  error: '#FF453A',
  tabBar: {
    active: '#0A84FF',
    inactive: '#98989F',
    background: '#1C1C1E',
    border: '#38383A',
  },
  statusBar: 'light',
} as const;

export type Theme = typeof lightTheme;
