import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Image,
  Alert,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useTheme } from '@/app/context/ThemeContext';
import { USERS } from '@/constants/Users';
import { UserId } from '@/types/user';

export default function AddTaskModal() {
  const { theme } = useTheme();
  const insets = useSafeAreaInsets();
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [selectedAssignee, setSelectedAssignee] = useState<UserId>(USERS.self.id);
  const spouse = USERS.others[0];

  const handleSave = () => {
    if (!title.trim()) {
      Alert.alert('Error', 'Please enter a task title');
      return;
    }

    // TODO: Save the task (you can pass this data back or use a store)
    console.log('Saving task:', { title, description, selectedAssignee });

    // Navigate back
    router.back();
  };

  const handleCancel = () => {
    if (title.trim() || description.trim()) {
      Alert.alert(
        'Discard Changes?',
        'You have unsaved changes. Are you sure you want to discard them?',
        [
          { text: 'Keep Editing', style: 'cancel' },
          { text: 'Discard', style: 'destructive', onPress: () => router.back() },
        ],
      );
    } else {
      router.back();
    }
  };

  return (
    <View style={[styles.container, { backgroundColor: theme.background }]}>
      <Stack.Screen
        options={{
          title: 'New Task',
          headerStyle: { backgroundColor: theme.background },
          headerTintColor: theme.text,
          headerTitleStyle: { fontWeight: '600', fontSize: 18 },
          headerLeft: () => (
            <TouchableOpacity onPress={handleCancel} style={styles.headerButton}>
              <Text style={[styles.cancelText, { color: theme.primary }]}>Cancel</Text>
            </TouchableOpacity>
          ),
          headerRight: () => (
            <TouchableOpacity
              onPress={handleSave}
              disabled={!title.trim()}
              style={[
                styles.saveButton,
                {
                  backgroundColor: title.trim() ? theme.primary : theme.border,
                },
              ]}
            >
              <Text
                style={[styles.saveText, { color: title.trim() ? '#FFFFFF' : theme.text + '60' }]}
              >
                Save
              </Text>
            </TouchableOpacity>
          ),
          presentation: 'modal',
          gestureEnabled: true,
        }}
      />

      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={styles.keyboardView}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={[styles.content, { paddingBottom: insets.bottom + 20 }]}
          showsVerticalScrollIndicator={false}
        >
          {/* Task Title Section */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.text }]}>Task Title</Text>
            <TextInput
              style={[
                styles.titleInput,
                {
                  backgroundColor: theme.card,
                  borderColor: theme.border,
                  color: theme.text,
                },
              ]}
              placeholder="What needs to be done?"
              placeholderTextColor={theme.text + '60'}
              value={title}
              onChangeText={setTitle}
              autoFocus
              returnKeyType="next"
              maxLength={100}
            />
          </View>

          {/* Description Section */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.text }]}>
              Description{' '}
              <Text style={[styles.optional, { color: theme.text + '60' }]}>(Optional)</Text>
            </Text>
            <TextInput
              style={[
                styles.descriptionInput,
                {
                  backgroundColor: theme.card,
                  borderColor: theme.border,
                  color: theme.text,
                },
              ]}
              placeholder="Add more details..."
              placeholderTextColor={theme.text + '60'}
              value={description}
              onChangeText={setDescription}
              multiline
              textAlignVertical="top"
              returnKeyType="done"
              maxLength={500}
            />
          </View>

          {/* Assignee Section */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.text }]}>Assign To</Text>
            <View style={styles.assigneeContainer}>
              {/* Self Option */}
              <TouchableOpacity
                style={[
                  styles.assigneeOption,
                  {
                    backgroundColor:
                      selectedAssignee === USERS.self.id ? theme.primary : theme.card,
                    borderColor: selectedAssignee === USERS.self.id ? theme.primary : theme.border,
                  },
                ]}
                onPress={() => setSelectedAssignee(USERS.self.id)}
                activeOpacity={0.7}
              >
                <Image source={{ uri: USERS.self.avatar }} style={styles.avatar} />
                <View style={styles.assigneeInfo}>
                  <Text
                    style={[
                      styles.assigneeName,
                      {
                        color: selectedAssignee === USERS.self.id ? '#FFFFFF' : theme.text,
                      },
                    ]}
                  >
                    {USERS.self.name}
                  </Text>
                  <Text
                    style={[
                      styles.assigneeRole,
                      {
                        color: selectedAssignee === USERS.self.id ? '#FFFFFF80' : theme.text + '60',
                      },
                    ]}
                  >
                    You
                  </Text>
                </View>
                {selectedAssignee === USERS.self.id && (
                  <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
                )}
              </TouchableOpacity>

              {/* Spouse Option */}
              <TouchableOpacity
                style={[
                  styles.assigneeOption,
                  {
                    backgroundColor: selectedAssignee === spouse.id ? theme.primary : theme.card,
                    borderColor: selectedAssignee === spouse.id ? theme.primary : theme.border,
                  },
                ]}
                onPress={() => setSelectedAssignee(spouse.id)}
                activeOpacity={0.7}
              >
                <Image source={{ uri: spouse.avatar }} style={styles.avatar} />
                <View style={styles.assigneeInfo}>
                  <Text
                    style={[
                      styles.assigneeName,
                      {
                        color: selectedAssignee === spouse.id ? '#FFFFFF' : theme.text,
                      },
                    ]}
                  >
                    {spouse.name}
                  </Text>
                  <Text
                    style={[
                      styles.assigneeRole,
                      {
                        color: selectedAssignee === spouse.id ? '#FFFFFF80' : theme.text + '60',
                      },
                    ]}
                  >
                    Partner
                  </Text>
                </View>
                {selectedAssignee === spouse.id && (
                  <Ionicons name="checkmark-circle" size={20} color="#FFFFFF" />
                )}
              </TouchableOpacity>
            </View>
          </View>

          {/* Quick Actions */}
          <View style={styles.section}>
            <Text style={[styles.label, { color: theme.text }]}>Quick Actions</Text>
            <View style={styles.quickActions}>
              <TouchableOpacity
                style={[
                  styles.quickAction,
                  { backgroundColor: theme.card, borderColor: theme.border },
                ]}
                onPress={() => setTitle('Buy groceries')}
              >
                <Ionicons name="basket-outline" size={20} color={theme.primary} />
                <Text style={[styles.quickActionText, { color: theme.text }]}>Groceries</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.quickAction,
                  { backgroundColor: theme.card, borderColor: theme.border },
                ]}
                onPress={() => setTitle('Clean house')}
              >
                <Ionicons name="home-outline" size={20} color={theme.primary} />
                <Text style={[styles.quickActionText, { color: theme.text }]}>Cleaning</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[
                  styles.quickAction,
                  { backgroundColor: theme.card, borderColor: theme.border },
                ]}
                onPress={() => setTitle('Pay bills')}
              >
                <Ionicons name="card-outline" size={20} color={theme.primary} />
                <Text style={[styles.quickActionText, { color: theme.text }]}>Bills</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  content: {
    padding: 20,
  },
  headerButton: {
    paddingHorizontal: 4,
  },
  cancelText: {
    fontSize: 16,
    fontWeight: '500',
  },
  saveButton: {
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 16,
  },
  saveText: {
    fontSize: 16,
    fontWeight: '600',
  },
  section: {
    marginBottom: 32,
  },
  label: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  optional: {
    fontWeight: '400',
  },
  titleInput: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    fontWeight: '500',
  },
  descriptionInput: {
    borderWidth: 1,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    height: 100,
  },
  assigneeContainer: {
    gap: 12,
  },
  assigneeOption: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
  },
  assigneeInfo: {
    flex: 1,
  },
  assigneeName: {
    fontSize: 16,
    fontWeight: '600',
  },
  assigneeRole: {
    fontSize: 14,
    marginTop: 2,
  },
  quickActions: {
    flexDirection: 'row',
    gap: 12,
  },
  quickAction: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 12,
    borderRadius: 12,
    borderWidth: 1,
    gap: 8,
  },
  quickActionText: {
    fontSize: 14,
    fontWeight: '500',
  },
});
