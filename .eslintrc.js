module.exports = {
  env: {
    browser: true,
    es2021: true,
    node: true,
  },
  ignorePatterns: [
    'package.json',
    'package-lock.json',
    '**/package.json',
    '**/package-lock.json',
  ],
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:jsx-a11y/recommended',
    'plugin:prettier/recommended', // Integrates <PERSON>ttier with ESLint
    'expo', // Expo's ESLint configuration
  ],
  parserOptions: {
    ecmaFeatures: {
      jsx: true, // Enables JSX
    },
    ecmaVersion: 12,
    sourceType: 'module',
  },
  plugins: [
    'react',
    'jsx-a11y',
    'prettier', // Prettier plugin
  ],
  rules: {
    'prettier/prettier': 'error', // Show Prettier errors as ESLint errors
    'react/react-in-jsx-scope': 'off', // Not needed with React 17+
    'import/prefer-default-export': 'off', // Allow named exports
    // Add or override additional rules as needed
  },
  settings: {
    react: {
      version: 'detect', // Automatically detect React version
    },
  },
};
