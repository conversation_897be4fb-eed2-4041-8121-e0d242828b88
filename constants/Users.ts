// constants/users.ts

import { UserId } from '@/types/user';

interface User {
  id: UserId;
  name: string;
  avatar: string;
  color: string;
}

export const USERS = {
  self: {
    id: 'user_1' as UserId,
    name: 'You',
    avatar: 'https://ui-avatars.com/api/?name=You',
    color: '#007AFF',
  },
  others: [
    {
      id: 'user_2' as UserId,
      name: '<PERSON>',
      avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>',
      color: '#34C759',
    },
  ],
};
