// constants/users.ts

import { UserId } from '@/types/user';

interface User {
  id: UserId;
  name: string;
  avatar: string;
  color: string;
}

export const USERS = {
  self: {
    id: '1',
    name: 'You',
    avatar: 'https://ui-avatars.com/api/?name=You',
    color: '#007AFF',
  },
  others: [
    {
      id: '2',
      name: '<PERSON>',
      avatar: 'https://ui-avatars.com/api/?name=<PERSON>',
      color: '#34C759',
    },
    {
      id: '3',
      name: '<PERSON>',
      avatar: 'https://ui-avatars.com/api/?name=<PERSON>+<PERSON>',
      color: '#FF9500',
    },
  ],
} as const;
