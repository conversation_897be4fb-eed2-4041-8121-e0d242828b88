import { useEffect, useState } from 'react';
import { Keyboard, Dimensions, Platform, KeyboardEvent } from 'react-native';

// Define the return type of the useKeyboardSpace hook
interface KeyboardSpace {
  keyboardHeight: number; // Height of the keyboard when it's visible
  availableSpace: number; // Space available on the screen not covered by the keyboard
  screenHeight: number; // Total height of the device's screen
}

/**
 * Custom hook to track the available screen space when the keyboard is visible.
 *
 * This hook listens for keyboard show and hide events and dynamically calculates
 * the available screen height that is not covered by the keyboard. It can be used
 * in modals, forms, or any components that need to adapt based on the keyboard's visibility.
 *
 * @returns {KeyboardSpace} - Returns an object containing:
 *   - `keyboardHeight`: The current height of the keyboard (0 if the keyboard is hidden).
 *   - `availableSpace`: The remaining screen height that is not covered by the keyboard.
 *   - `screenHeight`: The total height of the device's screen.
 *
 * @example
 * const { keyboardHeight, availableSpace, screenHeight } = useKeyboardSpace();
 * // Use these values to adjust component layout accordingly.
 */
const useKeyboardSpace = (): KeyboardSpace => {
  const screenHeight = Dimensions.get('window').height; // Get the device's screen height
  const [keyboardHeight, setKeyboardHeight] = useState<number>(0); // Track the keyboard height
  const [availableSpace, setAvailableSpace] = useState<number>(screenHeight); // Track available screen space

  useEffect(() => {
    // Determine the event names for keyboard show/hide based on the platform (iOS vs. Android)
    const showEvent =
      Platform.OS === 'ios' ? 'keyboardWillShow' : 'keyboardDidShow';
    const hideEvent =
      Platform.OS === 'ios' ? 'keyboardWillHide' : 'keyboardDidHide';

    // Event listener: triggered when the keyboard is shown
    const keyboardDidShowListener = Keyboard.addListener(
      showEvent,
      (event: KeyboardEvent) => {
        const newKeyboardHeight = event.endCoordinates.height; // Get the height of the keyboard
        setKeyboardHeight(newKeyboardHeight); // Update keyboard height
        setAvailableSpace(screenHeight - newKeyboardHeight); // Calculate space not covered by keyboard
      },
    );

    // Event listener: triggered when the keyboard is hidden
    const keyboardDidHideListener = Keyboard.addListener(hideEvent, () => {
      setKeyboardHeight(0); // Reset keyboard height
      setAvailableSpace(screenHeight); // Reset to full screen height
    });

    // Cleanup: remove event listeners when the component is unmounted or updated
    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, [screenHeight]);

  return { keyboardHeight, availableSpace, screenHeight };
};

export default useKeyboardSpace;
