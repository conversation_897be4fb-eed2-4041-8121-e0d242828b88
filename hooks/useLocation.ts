import { useState, useCallback } from 'react';
import * as Location from 'expo-location';
/**
 * Custom hook to request location permissions and fetch the user's current location.
 *
 * @returns {Object} - An object containing:
 *   - `location`: The current location (if available) as a `Location.LocationObject`, or `null`.
 *   - `errorMsg`: A string containing the error message if permission is denied or fetching fails, or `null`.
 *   - `loading`: A boolean representing whether the location fetching is in progress.
 *   - `fetchLocation`: A function to manually trigger the fetching of the user's location.
 */
export const useLocation = () => {
  const [location, setLocation] = useState<Location.LocationObject | null>(
    null,
  );
  const [errorMsg, setErrorMsg] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);

  /**
   * Fetches the user's current location.
   * This function requests foreground location permissions and, if granted,
   * retrieves the user's current location. If permissions are denied, it sets an error message.
   *
   * The function is memoized using `useCallback` to ensure that it doesn't get recreated
   * unnecessarily between renders.
   *
   * @async
   * @returns {Promise<void>}
   */
  const fetchLocation = useCallback(async () => {
    setLoading(true);
    let { status } = await Location.requestForegroundPermissionsAsync();
    if (status !== 'granted') {
      setErrorMsg('Permission to access location was denied');
      setLoading(false);
      return;
    }

    try {
      let location = await Location.getCurrentPositionAsync({});
      setLocation(location);
      setErrorMsg(null);
    } catch (error) {
      setErrorMsg('Failed to fetch location');
    } finally {
      setLoading(false);
    }
  }, []);

  return { location, errorMsg, loading, fetchLocation };
};
