import { useState, useEffect } from 'react';
import * as Notifications from 'expo-notifications';
import { Platform } from 'react-native';

/**
 * useNotification
 *
 * A custom hook to handle local notifications in a React Native Expo app.
 * This hook requests notification permissions, configures notification channels for Android,
 * and sets up a notification handler for iOS to ensure banner notifications are shown in the foreground.
 *
 * @param {Object} options - Customization options for foreground notification handling.
 * @param {boolean} [options.playSound=true] - Whether to play sound when receiving notifications.
 * @param {boolean} [options.setBadge=false] - Whether to set a badge on the app icon.
 *
 * @returns {Object} - Contains the permission status and a function to send local notifications.
 */
const useNotification = ({ playSound = true, setBadge = false } = {}) => {
  const [permissionStatus, setPermissionStatus] = useState<string | null>(null);

  useEffect(() => {
    /**
     * setupNotifications
     *
     * Handles the initial setup for notifications.
     * Requests permissions, sets up channels for Android, and configures notification handling for iOS.
     */
    const setupNotifications = async () => {
      const { status } = await Notifications.getPermissionsAsync();
      setPermissionStatus(status);

      if (status === 'undetermined') {
        const { status: newStatus } =
          await Notifications.requestPermissionsAsync();
        setPermissionStatus(newStatus);
      } else if (status === 'denied') {
        console.log('Notifications permission denied.');
      }

      if (Platform.OS === 'android') {
        await Notifications.setNotificationChannelAsync('default', {
          name: 'Default',
          importance: Notifications.AndroidImportance.HIGH,
        });
      }
    };

    setupNotifications();

    Notifications.setNotificationHandler({
      handleNotification: async () => ({
        shouldShowAlert: true,
        shouldPlaySound: playSound,
        shouldSetBadge: setBadge,
      }),
    });
  }, [playSound, setBadge]);

  /**
   * sendNotification
   *
   * Sends a local notification.
   *
   * @param {string} title - The title of the notification.
   * @param {string} body - The body content of the notification.
   * @param {boolean} sound - To play sound on the notification.
   * @param {Object} data - Additional data to pass with the notification (optional).
   */
  const sendNotification = async (
    title: string,
    body: string,
    sound: boolean = true,
    data = {},
  ) => {
    try {
      await Notifications.scheduleNotificationAsync({
        content: { title, body, sound: sound, data },
        trigger: { seconds: 1 },
      });
    } catch (error) {
      console.error('Error sending notification:', error);
    }
  };

  return { permissionStatus, sendNotification };
};

export default useNotification;
