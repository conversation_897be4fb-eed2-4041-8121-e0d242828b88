// hooks/usePartnerLocations.ts

import { useEffect } from 'react';
import { useTodoStore } from '@/store/todoStore';
import { Alert } from 'react-native';

const PARTNER_API_URL = 'https://your-backend-api.com/partners'; // Replace with your API endpoint

export const usePartnerLocations = () => {
  const setPartners = useTodoStore((state) => state.setPartners);
  const homeAddress = useTodoStore((state) => state.homeAddress);
  const zones = useTodoStore((state) => state.zones);
  const partners = useTodoStore((state) => state.partners);

  useEffect(() => {
    const fetchPartnerLocations = async () => {
      try {
        const response = await fetch(PARTNER_API_URL);
        const data = await response.json();
        // Assuming data is an array of partners
        setPartners(data);
        // Check each partner's location against zones
        data.forEach((partner: any) => {
          zones.forEach((zone) => {
            const distance = getDistanceFromLatLonInMeters(
              zone.center.latitude,
              zone.center.longitude,
              partner.location.latitude,
              partner.location.longitude,
            );
            if (distance <= zone.radius) {
              // Notify user
              Alert.alert(
                'Partner Alert',
                `${partner.name} has entered ${zone.radius} meters zone around your home.`,
              );
            }
          });
        });
      } catch (error) {
        console.error('Error fetching partner locations:', error);
      }
    };

    // Fetch immediately and then every 5 minutes
    fetchPartnerLocations();
    const interval = setInterval(fetchPartnerLocations, 5 * 60 * 1000); // 5 minutes

    return () => clearInterval(interval);
  }, [setPartners, zones]);

  // Helper function to calculate distance between two coordinates
  const getDistanceFromLatLonInMeters = (
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number => {
    const R = 6371e3; // Earth radius in meters
    const φ1 = (lat1 * Math.PI) / 180; // φ, λ in radians
    const φ2 = (lat2 * Math.PI) / 180;
    const Δφ = ((lat2 - lat1) * Math.PI) / 180;
    const Δλ = ((lon2 - lon1) * Math.PI) / 180;

    const a =
      Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
      Math.cos(φ1) * Math.cos(φ2) * Math.sin(Δλ / 2) * Math.sin(Δλ / 2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    const distance = R * c; // in meters
    return distance;
  };
};
