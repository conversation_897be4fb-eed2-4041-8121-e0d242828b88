// store/todoStore.ts

import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Define the Todo interface
export interface Todo {
  id: string;
  text: string;
  completed: boolean;
}

// Define the Zone interface
export interface Zone {
  id: string;
  radius: number; // in meters
  center: {
    latitude: number;
    longitude: number;
  };
}

// Define the Partner interface
export interface Partner {
  id: string;
  name: string;
  location: {
    latitude: number;
    longitude: number;
  };
  lastUpdated: number; // timestamp
}

// Define the TodoStore interface
interface TodoStore {
  todos: Todo[];
  loading: boolean;
  error: string | null;
  homeAddress: {
    latitude: number;
    longitude: number;
    address: string;
  } | null;
  zones: Zone[];
  partners: Partner[];
  // Actions
  addTodo: (text: string) => void;
  toggleTodo: (id: string) => void;
  removeTodo: (id: string) => void;
  setHomeAddress: (address: {
    latitude: number;
    longitude: number;
    address: string;
  }) => void;
  addZone: (zone: Zone) => void;
  setZones: (zones: Zone[]) => void;
  setPartners: (partners: Partner[]) => void;
  clearStore: () => void;
}

export const useTodoStore = create<TodoStore>()(
  persist(
    (set) => ({
      todos: [],
      loading: false,
      error: null,
      homeAddress: null,
      zones: [],
      partners: [],
      // Actions
      addTodo: (text: string) =>
        set((state) => ({
          todos: [
            ...state.todos,
            { id: Date.now().toString(), text, completed: false },
          ],
        })),
      toggleTodo: (id: string) =>
        set((state) => ({
          todos: state.todos.map((todo) =>
            todo.id === id ? { ...todo, completed: !todo.completed } : todo,
          ),
        })),
      removeTodo: (id: string) =>
        set((state) => ({
          todos: state.todos.filter((todo) => todo.id !== id),
        })),
      setHomeAddress: (address) => set({ homeAddress: address }),
      addZone: (zone: Zone) =>
        set((state) => ({
          zones: [...state.zones, zone],
        })),
      setZones: (zones: Zone[]) => set({ zones }),
      setPartners: (partners: Partner[]) => set({ partners }),
      clearStore: () =>
        set({
          todos: [],
          loading: false,
          error: null,
          homeAddress: null,
          zones: [],
          partners: [],
        }),
    }),
    {
      name: 'todo-storage', // Unique name for storage
      storage: createJSONStorage(() => AsyncStorage), // Correct property
      // Optionally, specify which parts of the state to persist
      // partialize: (state) => ({
      //   todos: state.todos,
      //   homeAddress: state.homeAddress,
      //   zones: state.zones,
      //   partners: state.partners,
      // }),
    },
  ),
);
